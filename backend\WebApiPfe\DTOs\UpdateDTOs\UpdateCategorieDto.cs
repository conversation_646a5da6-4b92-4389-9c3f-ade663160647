﻿using WebApiPfe.DTOs.ReadDTOs;
using System.ComponentModel.DataAnnotations;
namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class UpdateCategorieDto
    {
        [Required]
        public int Id { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Nom { get; set; }

        public string Description { get; set; }

        public bool EstValidee { get; set; }
    }
}
