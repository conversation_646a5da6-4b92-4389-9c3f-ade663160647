﻿namespace WebApiPfe.DTOs.ReadDTOs
{
    public class CommandeFournisseurDto
    {
        public int Id { get; set; }
        public string Reference { get; set; } = string.Empty;
        public int CommandeClientId { get; set; }
        public int FournisseurId { get; set; }
        public string NomFournisseur { get; set; } = string.Empty;
        public string MatriculeFiscale { get; set; } = string.Empty;
        public DateTime DateCreation { get; set; } // Changé de DateCommande à DateCreation
        public DateTime? DateLivraison { get; set; }
        public decimal FraisLivraison { get; set; }
        public string Statut { get; set; } = string.Empty;
        public string? NumeroBonLivraison { get; set; } = string.Empty;
        public IEnumerable<LigneCommandeFournisseurDto> LignesCommande { get; set; } = new List<LigneCommandeFournisseurDto>(); // Changé de Lignes à LignesCommande
        public decimal MontantTotal { get; set; }
    }
}
