{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\PFE\\backend\\WebApiPfe\\WebApiPfe.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\PFE\\backend\\WebApiPfe\\WebApiPfe.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\PFE\\backend\\WebApiPfe\\WebApiPfe.csproj", "projectName": "WebApiPfe", "projectPath": "C:\\Users\\<USER>\\source\\repos\\PFE\\backend\\WebApiPfe\\WebApiPfe.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\PFE\\backend\\WebApiPfe\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.Extensions.Identity.Core": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.SqlServer.Server": {"target": "Package", "version": "[1.0.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[8.0.7, )"}, "Stripe.net": {"target": "Package", "version": "[48.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[6.6.2, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[8.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}