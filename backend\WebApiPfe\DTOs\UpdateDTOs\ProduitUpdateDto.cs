﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class ProduitUpdateDto
    {
        [Required(ErrorMessage = "L'ID est obligatoire")]
        public int Id { get; set; }

        [StringLength(100, ErrorMessage = "Max 100 caractères")]

        public string Nom { get; set; }

        public string Description { get; set; }

        [Range(0.01, double.MaxValue)]
        public decimal? PrixVenteHT { get; set; }
        public int? Stock { get; set; }
        [Range(0, 100, ErrorMessage = "La remise doit être entre 0 et 100")]
        public decimal? PourcentageRemise { get; set; }
        public List<IFormFile>? ImageFiles { get; set; }
    }
}
