﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class AvisDto
    {
        public int Id { get; set; }

        [Range(1, 5, ErrorMessage = "La note doit être entre 1 et 5")]
        public int Note { get; set; }

        [StringLength(500, ErrorMessage = "Le commentaire ne peut dépasser 500 caractères")]
        public string? Commentaire { get; set; }

        public DateTime DatePublication { get; set; }

        public string ClientNom { get; set; } = string.Empty;
        public string ClientPrenom { get; set; } = string.Empty;
        public string ClientEmail { get; set; } = string.Empty;

        public int ProduitId { get; set; }
        public string ProduitNom { get; set; } = string.Empty;
    }
}
