﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class UpdateItemPanierDto
    {
        [Range(1, int.MaxValue, ErrorMessage = "La quantité doit être au moins 1")]
        public int? Quantite { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "Le prix après promotion doit être positif")]
        public decimal? PrixApresPromotion { get; set; }
    }
}
