﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class FormeDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Le nom est obligatoire")]
        [StringLength(100, ErrorMessage = "Max 100 caractères")]
        public required string Nom { get; set; }

        [Required(ErrorMessage = "La catégorie est obligatoire")]
        public int CategorieId { get; set; }

        [Required(ErrorMessage = "L'URL de l'image est obligatoire")]
        [Url(ErrorMessage = "URL invalide")]
        public required string ImageUrl { get; set; }

        // Variantes pour création/mise à jour
        public class Update : FormeDto { public new int Id { get; set; } }
    }
}
