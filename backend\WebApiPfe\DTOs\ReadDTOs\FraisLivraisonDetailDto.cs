namespace WebApiPfe.DTOs.ReadDTOs
{
    public class FraisLivraisonDetailDto
    {
        public int FournisseurId { get; set; }
        public string NomFournisseur { get; set; } = string.Empty;
        public decimal FraisLivraison { get; set; }
        public List<int> ProduitIds { get; set; } = new List<int>();
    }

    public class FraisLivraisonResponseDto
    {
        public List<FraisLivraisonDetailDto> FraisParFournisseur { get; set; } = new List<FraisLivraisonDetailDto>();
        public decimal FraisLivraisonTotal { get; set; }
    }
}
