﻿using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class PromotionDto : IProduitsApplicablesDto
    {
        public int Id { get; set; }
        public TypePromotion Type { get; set; }
        public decimal PourcentageRemise { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public string? CodePromo { get; set; }
        public int? CategorieId { get; set; }
        public int? SousCategorieId { get; set; }
        public int? MarqueId { get; set; }
        public int? FournisseurId { get; set; }
        public int? FormeId { get; set; }
        public bool AppliquerSurHT { get; set; }
        public bool EstValide { get; set; }
        public List<int>? ProduitsApplicablesIds { get; set; }
        public string? NomAffichage => $"{Type} -{PourcentageRemise}({DateDebut:dd/MM} au {DateFin:dd/MM})";
    }
}
