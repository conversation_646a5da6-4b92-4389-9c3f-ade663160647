﻿using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PromotionsController : ControllerBase
    {
        private readonly IPromotionService _promotionService;
        private readonly ILogger<PromotionsController> _logger;

        public PromotionsController(
            IPromotionService promotionService,
            ILogger<PromotionsController> logger)
        {
            _promotionService = promotionService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<PromotionDto>>> GetAllActivePromotions()
        {
            try
            {
                var promotions = await _promotionService.GetAllActiveAsync();
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions actives");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<PromotionDto>> GetPromotionById(int id)
        {
            try
            {
                var promotion = await _promotionService.GetByIdAsync(id);
                return Ok(promotion);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("produit/{produitId:int}")]
        public async Task<ActionResult<IEnumerable<PromotionDto>>> GetPromotionsForProduct(int produitId)
        {
            try
            {
                var promotions = await _promotionService.GetByProduitAsync(produitId);
                return Ok(promotions);
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Produit introuvable");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des promotions pour le produit {produitId}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpPost]
        public async Task<ActionResult<PromotionDto>> CreatePromotion([FromBody] CreatePromotionDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var createdPromotion = await _promotionService.CreateAsync(dto);
                return CreatedAtAction(
                    nameof(GetPromotionById),
                    new { id = createdPromotion.Id },
                    createdPromotion);
            }
            catch (ArgumentOutOfRangeException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return Conflict(ex.Message);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création d'une promotion");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdatePromotion(int id, [FromBody] CreatePromotionDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _promotionService.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<IActionResult> DeletePromotion(int id)
        {
            try
            {
                await _promotionService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("validate-code")]
        public async Task<ActionResult<bool>> ValidatePromoCode([FromQuery] string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return BadRequest("Le code promo est requis");

            try
            {
                var isValid = await _promotionService.ValidateCodePromoAsync(code);
                return Ok(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la validation du code promo {code}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("calculate-price")]
        public async Task<ActionResult<decimal>> CalculateFinalPrice(
            [FromQuery] int produitId,
            [FromQuery] string? codePromo = null)
        {
            try
            {
                var prixFinal = await _promotionService.GetPrixFinalAsync(produitId, codePromo);
                return Ok(prixFinal);
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Produit introuvable");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors du calcul du prix pour le produit {produitId}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }
    }
}
