﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class AvisUpdateDto
    {
        [Required(ErrorMessage = "L'ID de l'avis est obligatoire")]
        public int Id { get; set; }

        [Range(1, 5, ErrorMessage = "La note doit être entre 1 et 5")]
        public int? Note { get; set; }

        [StringLength(500, ErrorMessage = "Le commentaire ne peut dépasser 500 caractères")]
        public string? Commentaire { get; set; }
    }
}
