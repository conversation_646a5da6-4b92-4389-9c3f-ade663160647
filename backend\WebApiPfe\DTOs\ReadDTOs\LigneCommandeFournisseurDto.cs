﻿namespace WebApiPfe.DTOs.ReadDTOs
{
    public class LigneCommandeFournisseurDto
    {
        public int Id { get; set; }
        public int CommandeId { get; set; }
        public int ProduitId { get; set; }
        public string NomProduit { get; set; } = string.Empty;
        public string ReferenceProduit { get; set; } = string.Empty;
        public int Quantite { get; set; }
        public decimal PrixUnitaire { get; set; }
        public decimal TotalLigne { get; set; }
        public string? ImagePrincipale { get; set; }
    }
}
