﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models;
namespace WebApiPfe.DTOs.ReadDTOs
{
    public class CommandeDto
    {
        public int Id { get; set; }
        public int ClientId { get; set; }
        public DateTime DateCreation { get; set; }
        public string Statut { get; set; } = string.Empty;
        public decimal MontantTotal { get; set; }
        public decimal? MontantHT { get; set; }
        public decimal? MontantTVA { get; set; }
        public decimal? FraisLivraison { get; set; }
        public string? CodePromo { get; set; }
        public List<DetailsCommandeDto> Details { get; set; } = new();
        public List<PromotionUtiliseeDto>? PromotionsUtilisees { get; set; }
        public List<CommandeFournisseurDto>? CommandesFournisseurs { get; set; }
    }
}
