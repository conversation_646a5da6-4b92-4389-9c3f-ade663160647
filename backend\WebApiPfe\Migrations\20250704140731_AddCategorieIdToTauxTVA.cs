﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WebApiPfe.Migrations
{
    /// <inheritdoc />
    public partial class AddCategorieIdToTauxTVA : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CategorieId",
                table: "TauxTVA",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TauxTVA_CategorieId",
                table: "TauxTVA",
                column: "CategorieId");

            migrationBuilder.AddForeignKey(
                name: "FK_TauxTVA_Categories_CategorieId",
                table: "TauxTVA",
                column: "CategorieId",
                principalTable: "Categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TauxTVA_Categories_CategorieId",
                table: "TauxTVA");

            migrationBuilder.DropIndex(
                name: "IX_TauxTVA_CategorieId",
                table: "TauxTVA");

            migrationBuilder.DropColumn(
                name: "CategorieId",
                table: "TauxTVA");
        }
    }
}
