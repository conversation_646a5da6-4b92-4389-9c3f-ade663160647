﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class MarqueDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Le nom est obligatoire")]
        [StringLength(100, ErrorMessage = "Max 100 caractères")]
        public required string Name { get; set; }

        [Required(ErrorMessage = "Le logo est obligatoire")]
        [Url(ErrorMessage = "URL invalide")]
        public required string Logo { get; set; }

        // Variantes pour création/mise à jour
        public class Create : MarqueDto { }
        public class Update : MarqueDto { public new int Id { get; set; } }
    }
}
