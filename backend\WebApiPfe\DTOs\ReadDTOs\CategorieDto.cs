﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class CategorieDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Le nom est obligatoire")]
        [StringLength(100, MinimumLength = 3, ErrorMessage = "Le nom doit contenir entre 3 et 100 caractères")]
        public string Nom { get; set; }
        public string Description { get; set; }
        public bool EstValidee { get; set; }
        public int SousCategoriesCount { get; set; }
    }
}
