﻿using WebApiPfe.Models.Entity;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class ClientDto : UtilisateurBaseDto
    {
        public new DateTime DateInscription { get; set; }
        public int NombreCommandes { get; set; }
        public List<CommandeDto>? Commandes { get; set; }
        public List<AvisDto>? Avis { get; set; }
        public List<FavoriResponseDto>? Favoris { get; set; }
        public PanierDto? Panier { get; set; }
        public AdresseDto? AdresseLivraison { get; set; }
    }
}
