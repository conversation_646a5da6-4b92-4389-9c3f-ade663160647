using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.UpdateDTOs
{
    public class ClientProfileUpdateDto
    {
        [Required]
        [EmailAddress]
        public required string Email { get; set; }

        [Required, StringLength(50)]
        public required string Nom { get; set; }

        [Required, StringLength(50)]
        public required string Prenom { get; set; }

        [Required]
        [Phone(ErrorMessage = "Numéro de téléphone invalide")]
        public string? PhoneNumber { get; set; }
        
        public DateTime DateNaissance { get; set; }
    }
}
